import json
from typing import Optional

import httpx

from app.core.logging import get_logger

logger = get_logger(__name__)

async def remove_spans(data):
    """
    递归地从字典或列表中删除特定键。
    """
    if isinstance(data, dict):
        for key in ["ref_spans", "eq_spans", "authors", "bib_entries",
                    "year", "venue", "identifiers", "_pdf_hash", "header"]:
            data.pop(key, None)
        for key, value in data.items():
            data[key] = remove_spans(value)
    elif isinstance(data, list):
        return [remove_spans(item) for item in data]
    return data

async def process_pdf_json(input_json_path, output_json_path):
    """
    处理PDF JSON文件
    """
    logger.info(f"Paper2Code 开始处理 PDF JSON：{input_json_path}")
    try:
        with open(input_json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        cleaned_data = remove_spans(data)

        with open(output_json_path, 'w', encoding='utf-8') as f:
            json.dump(cleaned_data, f, indent=4)
        logger.info(f"Paper2Code 处理后的JSON保存到：{output_json_path}")
    except Exception as e:
        logger.error(f"Paper2Code 处理JSON失败：{e}")
        raise


async def paper2json(
        filename: str,
        file_content: bytes,
        file_content_type: Optional[str],
        json_file_path: str
):

    PARSER_API_URL = "http://192.168.10.131:8080"

    try:
        # 准备要发送给API的文件数据
        files = {"file": (filename, file_content, file_content_type)}

        async with httpx.AsyncClient(timeout=120) as client:
            logger.info(f"正在调用TO JSON API: {PARSER_API_URL}，上传文件: {filename}")

            # 发送POST请求
            response = await client.post(PARSER_API_URL, files=files)

            # 检查请求是否成功
            response.raise_for_status()

            # 解析返回的JSON数据
            json_data = response.json()

            # 将JSON数据写入文件
            with open(json_file_path, "w", encoding="utf-8") as json_file:
                json.dump(json_data, json_file, ensure_ascii=False, indent=4)

            logger.info(f"TO JSON API调用成功，JSON内容已保存至: {json_file_path}")
    except Exception as e:
        raise